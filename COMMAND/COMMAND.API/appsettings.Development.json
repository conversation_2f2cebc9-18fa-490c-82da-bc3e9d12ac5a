{
  "ConnectionStrings": {
    "ConnectionStrings": "Server=(local);Database=<PERSON>_bear;Uid=sa;Pwd=*****;Trust Server Certificate=True;",
    "Redis": "***************:6379,password=DoAn2025@,abortConnect=false"
  },
  "JwtOption": {
    "Issuer": "http://**************:0000",
    "Audience": "http://**************:000",
    "SecretKey": "IRanUIwukUBzSauFsZnr7AjV7XS96sun",
    "ExpireMin": 5
  },
  "MasstransitConfiguration": {
    "Host": "localhost",
    "VHost": "/",
    "Port": 5672,
    "UserName": "guest",
    "Password": "guest"
  },
  /*  "MasstransitConfiguration": {
      "Host": "***************",
      "VHost": "myHost",
      "Port": 5672,
      "UserName": "sa",
      "Password": "DoAn2025@"
    },*/
  "MessageBusOptions": {
    "retryLimit": 3,
    "initialInterval": "00:00:05",
    "intervalIncrement": "00:00:10"
  },
  "CloudinaryOptions": {
    "CloudName": "dvadlh7ah",
    "ApiKey": "611119568732129",
    "ApiSecret": "lvyo8r9YICLxWDnZq4UB4LeAVhE"
  },
  "MailOption": {
    "Mail": "<EMAIL>",
    "DisplayName": "Beautify System",
    "Password": "awonvixcvsnpgjxz",
    "Host": "smtp.gmail.com",
    "Port": 587
  },
  "SqlServerRetryOptions": {
    "MaxRetryCount": 5,
    "MaxRetryDelay": "00:00:05",
    "ErrorNumbersToAdd": []
  },
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console",
      "Serilog.Sinks.File"
    ],
    "MinimumLevel": {
      "Default": "Warning",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "Theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/log-.txt",
          "rollingInterval": "Day",
          "shared": true
        }
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId"
    ]
  },
  "PayOS": {
    "ClientId": "48df97f0-d4d8-4d46-ad4c-e892ff72d6f2",
    "ApiKey": "ace73bb7-ce86-47ea-98c5-59fb86259ae5",
    "ChecksumKey": "9760d8c0c56f92ac34b981d5e824866cfc821d0a3f66e64d8d2d8d8a88dedb24"
  },
  "AllowedHosts": "*",
  "Domain": "localhost:3000"
}
