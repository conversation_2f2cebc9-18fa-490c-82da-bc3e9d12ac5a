<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>


    <ItemGroup>
        <ProjectReference Include="..\..\CONTRACT\CONTRACT\CONTRACT.csproj" />
        <ProjectReference Include="..\COMMAND.CONTRACT\COMMAND.CONTRACT.csproj" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.7.0" />
        <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.7.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.7" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.7">
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
          <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>

</Project>
