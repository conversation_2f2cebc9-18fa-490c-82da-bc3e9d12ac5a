using CONTRACT.CONTRACT.DOMAIN.Abstractions.Aggregates;
using CONTRACT.CONTRACT.DOMAIN.Abstractions.Entities;

namespace CONTRACT.CONTRACT.DOMAIN.Entities;
public class Product : AggregateRoot<int>, IAuditableEntity
{
    public int Id { get; init; }
    public string Name { get; set; }
    public string Size { get; set; }
    public int Quantity { get; set; }
    public decimal Price { get; set; }
    public string Color { get; set; }

    public string ImgUrl { get; set; }

    // Navigation property for tags
    public virtual ICollection<ProductTag> ProductTags { get; init; } = new List<ProductTag>();
    public DateTimeOffset CreatedOnUtc { get; set; }
    public DateTimeOffset? ModifiedOnUtc { get; set; }
    public DateTimeOffset? DeletedOnUtc { get; set; }
}
