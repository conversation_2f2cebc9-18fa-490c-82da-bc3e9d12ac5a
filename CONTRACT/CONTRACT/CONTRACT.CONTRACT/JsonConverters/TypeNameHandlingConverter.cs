using Newtonsoft.Json;

namespace CONTRACT.CONTRACT.CONTRACT.JsonConverters;
public class TypeNameHandlingConverter(TypeNameHandling typeNameHandling) : JsonConverter
{
    public override void Write<PERSON>son(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        new JsonSerializer { TypeNameHandling = typeNameHandling }.Serialize(writer, value);
    }

    public override object? Read<PERSON><PERSON>(JsonReader reader, Type type, object? existingValue, JsonSerializer serializer)
    {
        return new JsonSerializer { TypeNameHandling = typeNameHandling }.Deserialize(reader, type);
    }

    public override bool CanConvert(Type type)
    {
        return IsMassTransitOrSystemType(type) is false;
    }

    private static bool IsMassTransitOrSystemType(Type type)
    {
        return (type.Assembly.FullName?.Contains(nameof(MassTransit)) ?? false) ||
               type.Assembly.IsDynamic ||
               type.Assembly == typeof(object).Assembly;
    }
}