{"ConnectionStrings": {"ConnectionStrings": "Server=**************;Database=PRN_SUPER;Uid=sa;Pwd=MyStrongPassword123@;Trust Server Certificate=True;", "Redis": "**************:6379,password=MyStrongPassword123@,abortConnect=false"}, "JwtOption": {"Issuer": "http://**************:8080", "Audience": "http://**************:8080", "SecretKey": "IRanUIwukUBzSauFsZnr7AjV7XS96sun", "ExpireMin": 5}, "MasstransitConfiguration": {"Host": "**************", "VHost": "myHost", "Port": 5672, "UserName": "sa", "Password": "MyStrongPassword123@"}, "MessageBusOptions": {"retryLimit": 3, "initialInterval": "00:00:05", "intervalIncrement": "00:00:10"}, "SqlServerRetryOptions": {"MaxRetryCount": 5, "MaxRetryDelay": "00:00:05", "ErrorNumbersToAdd": []}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"Theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console"}}, {"Name": "File", "Args": {"path": "logs/log-.txt", "rollingInterval": "Day", "shared": true}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "MongoDbSettings": {"ConnectionString": "mongodb://sa:MyStrongPassword123%40@**************:27017/", "DatabaseName": "PRNSUPER"}, "AllowedHosts": "*"}