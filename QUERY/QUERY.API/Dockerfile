FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Copy project files
COPY ["QUERY/QUERY.API/QUERY.API.csproj", "QUERY/QUERY.API/"]
COPY ["QUERY/QUERY.APPLICATION/QUERY.APPLICATION.csproj", "QUERY/QUERY.APPLICATION/"]
COPY ["QUERY/QUERY.CONTRACT/QUERY.CONTRACT.csproj", "QUERY/QUERY.CONTRACT/"]
COPY ["QUERY/QUERY.INFRASTRUCTURE/QUERY.INFRASTRUCTURE.csproj", "QUERY/QUERY.INFRASTRUCTURE/"]
COPY ["QUERY/QUERY.PERSISTENCE/QUERY.PERSISTENCE.csproj", "QUERY/QUERY.PERSISTENCE/"]
COPY ["QUERY/QUERY.PRESENTATION/QUERY.PRESENTATION.csproj", "QUERY/QUERY.PRESENTATION/"]
COPY ["CONTRACT/CONTRACT/CONTRACT.csproj", "CONTRACT/CONTRACT/"]
COPY ["E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/ServiceDefaults.csproj", "E_COMMERCE_TEDDYBEAR_SHOP.ServiceDefaults/"]

# Restore dependencies
RUN dotnet restore "QUERY/QUERY.API/QUERY.API.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/QUERY/QUERY.API"
RUN dotnet build "QUERY.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "QUERY.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "QUERY.API.dll"]
